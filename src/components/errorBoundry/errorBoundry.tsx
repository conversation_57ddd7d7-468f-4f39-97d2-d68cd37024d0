import React, { Component, ReactNode } from 'react';
import { useRouteError } from 'react-router-dom';
import * as Sentry from '@sentry/react';
import { Button, Result } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
    children: ReactNode;
}

/**
 * React Error Boundary component that catches unhandled JavaScript errors
 * in the component tree and displays a user-friendly error message in Russian.
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        // Update state so the next render will show the fallback UI
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        // Log error details for debugging
        this.setState({ error, errorInfo });

        // Report error to Sentry with context
        Sentry.withScope((scope) => {
            scope.setTag('errorBoundary', true);
            scope.setLevel('error');
            scope.setContext('errorInfo', {
                componentStack: errorInfo.componentStack,
                errorBoundary: true,
            });
            scope.setContext('errorDetails', {
                message: error.message,
                stack: error.stack,
                name: error.name,
            });
            Sentry.captureException(error);
        });
    }

    handleReload = () => {
        // Reset error state and reload the page
        this.setState({ hasError: false, error: undefined, errorInfo: undefined });
        window.location.reload();
    };

    render() {
        if (this.state.hasError) {
            return (
                <div
                    style={{
                        height: '100vh',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: '20px',
                    }}
                >
                    <Result
                        status="500"
                        title="Произошла ошибка"
                        subTitle="Извините, что-то пошло не так. Попробуйте перезагрузить страницу."
                        extra={
                            <Button
                                type="primary"
                                icon={<ReloadOutlined />}
                                onClick={this.handleReload}
                            >
                                Перезагрузить страницу
                            </Button>
                        }
                    />
                </div>
            );
        }

        return this.props.children;
    }
}

/**
 * Router-level error boundary for React Router errors
 * This is used as errorElement in React Router configuration
 */
export function YourCustomRootErrorBoundary() {
    const error = useRouteError() as Error;

    // Report router errors to Sentry
    React.useEffect(() => {
        if (error) {
            Sentry.withScope((scope) => {
                scope.setTag('routerError', true);
                scope.setLevel('error');
                scope.setContext('routerErrorDetails', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name,
                    isRouterError: true,
                });
                Sentry.captureException(error);
            });
        }
    }, [error]);

    const handleReload = () => {
        window.location.reload();
    };

    return (
        <div
            style={{
                height: '100vh',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '20px',
            }}
        >
            <Result
                status="500"
                title="Произошла ошибка"
                subTitle="Извините, что-то пошло не так. Попробуйте перезагрузить страницу."
                extra={
                    <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleReload}
                    >
                        Перезагрузить страницу
                    </Button>
                }
            />
        </div>
    );
}

export { ErrorBoundary };
