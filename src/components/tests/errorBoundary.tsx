import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Col, Divider, Row, Space, Typography } from 'antd';
import { BugOutlined, ReloadOutlined, WarningOutlined } from '@ant-design/icons';
import UniLayout from '@components/ui/uniLayout/uniLayout';
import { ErrorBoundary } from '@components/errorBoundry/errorBoundry';

import './tests.scss';

const { Title, Paragraph, Text } = Typography;

// Component that throws an error when clicked
const ErrorThrowingComponent: React.FC<{ shouldThrow: boolean }> = ({ shouldThrow }) => {
    if (shouldThrow) {
        throw new Error('Тестовая ошибка для демонстрации Error Boundary');
    }
    
    return (
        <Card>
            <Text type="success">✅ Компонент работает нормально</Text>
        </Card>
    );
};

// Component that throws an error in useEffect
const AsyncErrorComponent: React.FC<{ shouldThrow: boolean }> = ({ shouldThrow }) => {
    React.useEffect(() => {
        if (shouldThrow) {
            // Simulate async error
            setTimeout(() => {
                throw new Error('Асинхронная ошибка в useEffect');
            }, 100);
        }
    }, [shouldThrow]);
    
    return (
        <Card>
            <Text type="success">✅ Асинхронный компонент работает</Text>
        </Card>
    );
};

const ErrorBoundaryTest: React.FC = () => {
    const [throwRenderError, setThrowRenderError] = useState(false);
    const [throwAsyncError, setThrowAsyncError] = useState(false);
    const [resetKey, setResetKey] = useState(0);

    const handleReset = () => {
        setThrowRenderError(false);
        setThrowAsyncError(false);
        setResetKey(prev => prev + 1);
    };

    return (
        <UniLayout>
            <div style={{ padding: '24px' }}>
                <Title level={2}>
                    <BugOutlined /> Тестирование Error Boundary
                </Title>
                
                <Paragraph>
                    Эта страница демонстрирует работу компонента Error Boundary, который перехватывает 
                    необработанные JavaScript ошибки в дереве компонентов и отображает пользователю 
                    дружелюбное сообщение на русском языке.
                </Paragraph>

                <Row gutter={[16, 16]}>
                    <Col span={24}>
                        <Card title="Управление тестами">
                            <Space wrap>
                                <Button 
                                    type="primary" 
                                    danger
                                    icon={<WarningOutlined />}
                                    onClick={() => setThrowRenderError(true)}
                                    disabled={throwRenderError}
                                >
                                    Вызвать ошибку рендера
                                </Button>
                                
                                <Button 
                                    type="primary" 
                                    danger
                                    icon={<WarningOutlined />}
                                    onClick={() => setThrowAsyncError(true)}
                                    disabled={throwAsyncError}
                                >
                                    Вызвать асинхронную ошибку
                                </Button>
                                
                                <Button 
                                    icon={<ReloadOutlined />}
                                    onClick={handleReset}
                                >
                                    Сбросить тесты
                                </Button>
                            </Space>
                        </Card>
                    </Col>
                </Row>

                <Divider />

                <Row gutter={[16, 16]}>
                    <Col span={12}>
                        <Title level={4}>Тест 1: Ошибка рендера</Title>
                        <Paragraph type="secondary">
                            Error Boundary перехватывает ошибки, возникающие во время рендера компонентов.
                        </Paragraph>
                        
                        <ErrorBoundary key={`render-${resetKey}`}>
                            <ErrorThrowingComponent shouldThrow={throwRenderError} />
                        </ErrorBoundary>
                    </Col>
                    
                    <Col span={12}>
                        <Title level={4}>Тест 2: Асинхронная ошибка</Title>
                        <Paragraph type="secondary">
                            ⚠️ Error Boundary НЕ перехватывает асинхронные ошибки (setTimeout, Promise, etc).
                            Такие ошибки будут отправлены в Sentry через глобальный обработчик.
                        </Paragraph>
                        
                        <ErrorBoundary key={`async-${resetKey}`}>
                            <AsyncErrorComponent shouldThrow={throwAsyncError} />
                        </ErrorBoundary>
                    </Col>
                </Row>

                <Divider />

                <Card title="Информация о Error Boundary">
                    <Title level={5}>Что перехватывает Error Boundary:</Title>
                    <ul>
                        <li>Ошибки в методах рендера компонентов</li>
                        <li>Ошибки в конструкторах компонентов</li>
                        <li>Ошибки в методах жизненного цикла</li>
                    </ul>

                    <Title level={5}>Что НЕ перехватывает Error Boundary:</Title>
                    <ul>
                        <li>Ошибки в обработчиках событий (onClick, onChange, etc)</li>
                        <li>Асинхронные ошибки (setTimeout, requestAnimationFrame, Promise)</li>
                        <li>Ошибки в самом Error Boundary</li>
                        <li>Ошибки на стороне сервера</li>
                    </ul>

                    <Title level={5}>Логирование ошибок:</Title>
                    <ul>
                        <li>Все перехваченные ошибки автоматически отправляются в Sentry с контекстом</li>
                        <li>Пользователю отображается дружелюбное сообщение на русском языке</li>
                        <li>Предоставляется кнопка для перезагрузки страницы</li>
                    </ul>
                </Card>
            </div>
        </UniLayout>
    );
};

export default ErrorBoundaryTest;
